#include "date.h"


Date::Date(int y, int m, int d) : year(y), month(m), day(d) {
    totalDays = 365 * (year - 1) + leapYears(year - 1) + monthDays(month - 1) + day;
}

int Date::leapYears(int n) const {
    return n / 4 - n / 100 + n / 400;
}

int Date::monthDays(int m) const {
    static const int days[] = {0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334, 365};
    int res = days[m];
    if (m > 2 && isLeapYear()) res += 1; // 闰年2月多一天
    return res;
}

void Date::show() const {
    cout << year << "-" << month << "-" << day;
}

bool Date::isLeapYear() const {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

int Date::distance(const Date& date) const {
    return abs(totalDays - date.totalDays);
}

// 重载"-"运算符实现
int Date::operator-(const Date& date) const {
    return abs(totalDays - date.totalDays);
}

int Date::getYear() const { return year; }
int Date::getMonth() const { return month; }
int Date::getDay() const { return day; }
