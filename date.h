#ifndef _DATE_H_
#define _DATE_H_

#include <iostream>
using namespace std;

class Date {
private:
    int year, month, day;
    int totalDays; // 从公元元年1月1日开始的总天数

    // 计算前n年的闰年数
    int leapYears(int n) const;

    // 计算当年前m个月的天数（考虑闰年）
    int monthDays(int m) const;

public:
    Date(int y, int m, int d);

    int getYear() const;
    int getMonth() const;
    int getDay() const;

    void show() const;
    bool isLeapYear() const;
    int distance(const Date& date) const;
};

#endif 