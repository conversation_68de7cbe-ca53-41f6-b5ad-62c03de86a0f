#ifndef _ACCOUNT_H_
#define _ACCOUNT_H_

#include <iostream>
#include "date.h"
using namespace std;

// 账户基类
class Account {
protected:
    int id;          // 账号
    double balance;  // 余额
    static double total; // 所有账户总金额

public:
    Account(Date date, int id);
    virtual ~Account() {}

    // 纯虚函数声明
    virtual void deposit(Date date, double amount) = 0;
    virtual void withdraw(Date date, double amount) = 0;
    virtual void settle(Date date) = 0;

    // 输出账户信息（虚函数）
    virtual void show() const;

    // 常成员函数
    double getBalance() const;
    int getId() const;

    // 静态成员函数
    static double getTotal();
};

// 储蓄账户类
class SavingsAccount : public Account {
private:
    double rate;     // 年利率
    Date lastDate;   // 上次变更余额的日期
    double accumulation; // 余额按日累加之和

    // 计算到指定日期的累积值
    double accumulate(const Date& date) const;

    // 更新累积值（私有成员函数）
    void update(const Date& date);

public:
    SavingsAccount(Date date, int id, double rate);

    // 存款操作
    void deposit(Date date, double amount) override;

    // 取款操作
    void withdraw(Date date, double amount) override;

    // 结算利息（每年1月1日调用）
    void settle(Date date) override;

    // 输出账户信息（常成员函数）
    virtual void show() const override;

    // 常成员函数
    double getRate() const;
};

// 信用账户类
class CreditAccount : public Account {
private:
    double credit;   // 信用额度
    double rate;     // 欠款的日利率
    double fee;      // 信用卡年费
    Date lastDate;   // 上次变更余额的日期
    
    // 获得欠款额
    double getDebt() const;
    
    // 更新累积值（私有成员函数）
    void update(const Date& date);

public:
    CreditAccount(Date date, int id, double credit, double rate, double fee);
    
    // 获得可用信用额度
    double getAvailableCredit() const;
    
    // 取款操作
    void withdraw(Date date, double amount) override;

    // 存款操作
    void deposit(Date date, double amount) override;

    // 结算利息和年费
    void settle(Date date) override;
    
    // 输出账户信息
    virtual void show() const override;
};

#endif 
