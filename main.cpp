#include <iostream>
#include "date.h"
#include "account.h"
using namespace std;

int main() {
    // 创建账户
    Date openDate(2020, 11, 1);
    SavingsAccount s0(openDate, 1001, 0.015);
    SavingsAccount s1(openDate, 1002, 0.015);
    CreditAccount c0(openDate, 1003, 10000, 0.0005, 50);

    // 账户操作
    s0.deposit(Date(2020, 11, 5), 5000);
    s0.deposit(Date(2020, 12, 5), 5500);

    s1.deposit(Date(2020, 11, 25), 10000);
    s1.withdraw(Date(2020, 12, 20), 4000);

    c0.withdraw(Date(2020, 11, 15), 2000);
    c0.deposit(Date(2020, 12, 1), 2000); // 还清欠款

    // 结算
    Date settleDate(2021, 1, 1);
    s0.settle(settleDate);
    s1.settle(settleDate);
    c0.settle(settleDate);

    // 输出账户信息
    cout << "\n--- 账户信息 ---\n";
    s0.show();
    s1.show();
    c0.show();
    cout << "\n所有账户总金额: " << Account::getTotal() << endl;

    return 0;
}