#include <iostream>
#include <vector>
#include "date.h"
#include "account.h"
using namespace std;

int main() {
    // 创建账户
    Date openDate(2020, 11, 1);
    SavingsAccount s0(openDate, 1001, 0.015);
    SavingsAccount s1(openDate, 1002, 0.015);
    CreditAccount c0(openDate, 1003, 10000, 0.0005, 50);

    // 测试运算符重载
    cout << "=== 测试日期运算符重载 ===" << endl;
    Date date1(2020, 11, 1);
    Date date2(2020, 12, 1);
    cout << "日期1: ";
    date1.show();
    cout << "\n日期2: ";
    date2.show();
    cout << "\n两日期相差天数（使用operator-）: " << (date2 - date1) << " 天" << endl;
    cout << "两日期相差天数（使用distance）: " << date1.distance(date2) << " 天" << endl;
    cout << endl;

    // 账户操作
    s0.deposit(Date(2020, 11, 5), 5000);
    s0.deposit(Date(2020, 12, 5), 5500);

    s1.deposit(Date(2020, 11, 25), 10000);
    s1.withdraw(Date(2020, 12, 20), 4000);

    c0.withdraw(Date(2020, 11, 15), 2000);
    c0.deposit(Date(2020, 12, 1), 2000); // 还清欠款

    // 测试多态性 - 使用基类指针
    cout << "\n=== 测试多态性（使用基类指针） ===" << endl;
    vector<Account*> accounts;
    accounts.push_back(&s0);
    accounts.push_back(&s1);
    accounts.push_back(&c0);

    // 通过基类指针调用虚函数
    cout << "\n--- 通过基类指针进行操作 ---" << endl;
    for (Account* acc : accounts) {
        acc->deposit(Date(2020, 12, 25), 1000);
    }

    // 结算 - 统一在每月1日调用
    Date settleDate(2021, 1, 1);
    cout << "\n--- 通过基类指针进行结算 ---" << endl;
    for (Account* acc : accounts) {
        acc->settle(settleDate);
    }

    // 输出账户信息 - 通过基类指针调用虚函数show
    cout << "\n--- 通过基类指针显示账户信息 ---" << endl;
    for (Account* acc : accounts) {
        acc->show();
        cout << endl;
    }

    cout << "所有账户总金额: " << Account::getTotal() << endl;

    return 0;
}