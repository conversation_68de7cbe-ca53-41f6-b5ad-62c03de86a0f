#include "account.h"

// 静态成员初始化
double Account::total = 0.0;

// Account类实现
Account::Account(Date date, int id) : id(id), balance(0) {}

void Account::show() const {
    cout << "账号: " << id << " 余额: " << balance << endl;
}

double Account::getBalance() const { return balance; }
int Account::getId() const { return id; }
double Account::getTotal() { return total; }

// SavingsAccount类实现
SavingsAccount::SavingsAccount(Date date, int id, double rate)
    : Account(date, id), rate(rate), lastDate(date), accumulation(0) {}

double SavingsAccount::accumulate(const Date& date) const {
    return accumulation + balance * (date - lastDate);
}

void SavingsAccount::update(const Date& date) {
    if ((date - lastDate) > 0) {
        accumulation = accumulate(date);
        lastDate = date;
    }
}

void SavingsAccount::deposit(Date date, double amount) {
    update(date);
    balance += amount;
    total += amount; 
    cout << date.getYear() << "-" << date.getMonth() << "-" << date.getDay() 
         << ": 账号" << id << " 存入" << amount << " 余额" << balance << endl;
}

void SavingsAccount::withdraw(Date date, double amount) {
    update(date);
    if (amount > balance) {
        cout << "余额不足！" << endl;
        return;
    }
    balance -= amount;
    total -= amount; 
    cout << date.getYear() << "-" << date.getMonth() << "-" << date.getDay() 
         << ": 账号" << id << " 取出" << amount << " 余额" << balance << endl;
}

void SavingsAccount::settle(Date date) {
    // 储蓄账户只在1月份进行利息结算
    if (date.getMonth() == 1) {
        double interest = accumulate(date) * rate / 365;
        if (interest > 0) {
            update(date);
            balance += interest;
            total += interest;
            cout << date.getYear() << "-" << date.getMonth() << "-" << date.getDay()
                 << ": 账号" << id << " 结算利息" << interest << " 余额" << balance << endl;
        }
        accumulation = 0;
    }
}

void SavingsAccount::show() const {
    Account::show();
    cout << "年利率: " << rate * 100 << "%" << endl;
}

double SavingsAccount::getRate() const { return rate; }

// CreditAccount类实现
CreditAccount::CreditAccount(Date date, int id, double credit, double rate, double fee)
    : Account(date, id), credit(credit), rate(rate), fee(fee), lastDate(date) {}

double CreditAccount::getDebt() const {
    return balance < 0 ? -balance : 0;
}

double CreditAccount::getAvailableCredit() const {
    return credit + balance < 0 ? 0 : credit + balance;
}

void CreditAccount::update(const Date& date) {
    if ((date - lastDate) > 0) {
        double debt = getDebt();
        if (debt > 0) {
            double interest = debt * rate * (date - lastDate);
            balance -= interest;
            total -= interest;
        }
        lastDate = date;
    }
}

void CreditAccount::withdraw(Date date, double amount) {
    update(date);
    if (amount > getAvailableCredit()) {
        cout << "可用信用额度不足！" << endl;
        return;
    }
    balance -= amount;
    total -= amount;
    cout << date.getYear() << "-" << date.getMonth() << "-" << date.getDay() 
         << ": 账号" << id << " 取出" << amount << " 余额" << balance 
         << " 可用信用额度" << getAvailableCredit() << endl;
}

void CreditAccount::deposit(Date date, double amount) {
    update(date);
    balance += amount;
    total += amount;
    cout << date.getYear() << "-" << date.getMonth() << "-" << date.getDay() 
         << ": 账号" << id << " 存入" << amount << " 余额" << balance 
         << " 可用信用额度" << getAvailableCredit() << endl;
}

void CreditAccount::settle(Date date) {
    double debt = getDebt();
    if (debt > 0) {
        double interest = debt * rate * (date - lastDate);
        balance -= interest;
        total -= interest;
        cout << date.getYear() << "-" << date.getMonth() << "-" << date.getDay()
             << ": 账号" << id << " 结算利息" << interest << " 余额" << balance << endl;
    }

    // 每年1月1日收取年费
    if (date.getMonth() == 1 && date.getDay() == 1) {
        balance -= fee;
        total -= fee;
        cout << date.getYear() << "-" << date.getMonth() << "-" << date.getDay()
             << ": 账号" << id << " 支付年费" << fee << " 余额" << balance << endl;
    }

    lastDate = date;
}

void CreditAccount::show() const {
    Account::show();
    cout << "信用额度: " << credit 
         << " 可用信用额度: " << getAvailableCredit()
         << " 日利率: " << rate * 100 << "%" << endl;
}
